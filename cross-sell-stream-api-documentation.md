# Cross-Sell Stream API Documentation

## Overview

The Cross-Sell Stream API provides real-time streaming of hotel cross-selling recommendations using Server-Sent Events (SSE) with HTTP chunked transfer encoding. The API delivers personalized hotel recommendations as they become available, supporting parallel processing for multiple search events.

## Endpoints

### V1 Cross-Sell Stream
- **URL:** `POST /hotels-scion/api/cross-sell-stream`
- **Handler:** `HandleCrossSellStreamRequest`

### V2 Cross-Sell Stream (Enhanced)
- **URL:** `POST /hotels-scion/api/v2/cross-sell-stream`
- **Handler:** `HandleCrossSellV2StreamRequest`
- **Features:** Rule engine support, benefit cards, extended trip recommendations

## Architecture

### Type
- **Streaming API** with chunked transfer encoding
- **Pattern:** Server-Sent Events (SSE) with HTTP streaming
- **Concurrency:** Goroutine-based parallel processing for each search event
- **Response:** Real-time streaming of cross-sell recommendations

### Dependencies

| Service | URL | Purpose |
|---------|-----|---------|
| **ClientGateway Search Hotels** | `http://hotels-clientgateway.ecs.mmt/clientbackend/entity/api/searchHotels` | Hotel search functionality |
| **ClientBackend Cross-sell Static Details** | `http://mmtclient-bkend.mmt.mmt/clientbackend/entity/api/xsell/staticDetail` | Static hotel details for cross-selling |
| **Singularity Recommendation Engine** | `http://hotels-singularity.ecs.mmt/Hotels-Singularity/v1/getHotelFromPriorBooking` | AI-powered recommendations |
| **Web API Cross-sell Drools** | `http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/crossSell` | Cross-sell rule engine (V2 only) |
| **Couchbase Cache** | `cb-42-70.mmt.mmt,cb-42-71.mmt.mmt,cb-42-72.mmt.mmt,cb-42-73.mmt.mmt` | Caching layer for performance |

## Request Format

### Headers
- **Content-Type:** `application/json` (required)
- **Transfer-Encoding:** `chunked` (recommended)
- **profileType:** User profile type (`BUSINESS` for corporate users)
- **mmt-auth:** Authentication token
- **region:** Site domain/region
- **uuid:** User UUID
- **ver:** App version

### Request Body Structure

```json
{
  "brand": "string",
  "context": {
    "pageContext": "string",
    "experimentData": "string"
  },
  "secureUrl": "string",
  "imageCount": 0,
  "searchEvent": [
    {
      "cardId": "string",
      "sc": {
        "from": {
          "locus": {
            "id": "string"
          }
        },
        "to": {
          "locus": {
            "id": "string"
          }
        },
        "fromDateTime": {
          "str": "string"
        },
        "toDateTime": {
          "str": "string"
        },
        "pax": [
          {
            "count": 0,
            "details": {
              "adult": {
                "count": 0,
                "ages": [0]
              }
            }
          }
        ]
      },
      "hydraSegments": ["string"],
      "limit": 0,
      "selectedTabId": "string",
      "appliedFilterMap": {},
      "funnelActivity": {},
      "enrichments": {
        "trends": {
          "areas": [],
          "price_bucket": [],
          "ap_bucket": [],
          "los_bucket": [],
          "pax": [],
          "pd_id": [],
          "star_rating": [],
          "accommodation_type": [],
          "cities": []
        },
        "userPreferences": {}
      },
      "tags": {
        "edge_type": "string",
        "source": "string"
      },
      "filterList": [],
      "filter": false,
      "persuasionRequired": false,
      "recentlyViewedHotels": [],
      "isLandingDiscoveryPage": false,
      "recommendedPlaces": []
    }
  ],
  "nearBy": false,
  "offerRequired": false,
  "user": {
    "location": {
      "locationData": {
        "currentLocation": {
          "latitude": 19.076,
          "longitude": 72.8777
        }
      }
    }
  },
  "imageCategories": [],
  "correlationKey": "string",
  "metaInfo": {
    "userName": "string",
    "bookingId": "string"
  }
}
```

### Required Fields
- `brand`: Brand identifier (e.g., "MMT", "GOIBIBO")
- `searchEvent`: Array of search events to process
- `searchEvent[].cardId`: Card type identifier
- `searchEvent[].sc`: Search criteria with from/to locations and dates

### Optional Fields
- `correlationKey`: Request correlation identifier (auto-generated if not provided)
- `context.pageContext`: Page context for recommendations
- `context.experimentData`: A/B testing experiment data
- `user`: User location and preferences
- `imageCount`: Number of images to return per hotel
- `nearBy`: Include nearby hotels flag
- `offerRequired`: Include offer details flag

## Response Format

### V1 Response Structure
```json
{
  "error": "string",
  "status": "string",
  "message": "string",
  "cards": [
    {
      "cardId": "string",
      "heading": "string",
      "hotels": [
        {
          "id": "string",
          "name": "string",
          "images": ["string"],
          "starRating": 0,
          "userRating": 0.0,
          "currencyCode": "string",
          "address": {
            "line1": "string",
            "line2": "string"
          },
          "displayFare": {
            "displayPrice": 0.0,
            "nonDiscountedPrice": 0.0
          },
          "appDeeplink": "string",
          "desktopDeeplink": "string"
        }
      ],
      "searchContext": {},
      "viewMore": {},
      "moreDetails": "string",
      "offerDetails": {
        "description": "string"
      }
    }
  ]
}
```

### V2 Response Structure
```json
{
  "cardsV2": [
    {
      "cardDataV2": {
        "tabsData": [
          {
            "tabId": "string",
            "tabName": "string",
            "hotels": [
              {
                "id": "string",
                "name": "string",
                "images": ["string"],
                "starRating": 0,
                "userRating": 0.0,
                "displayFare": {
                  "displayPrice": 0.0,
                  "nonDiscountedPrice": 0.0
                },
                "benefits": ["string"],
                "extendedTripData": {}
              }
            ]
          }
        ],
        "ctaList": [
          {
            "text": "string",
            "action": "string",
            "deeplink": "string"
          }
        ],
        "bgLinearGradient": {
          "colors": ["string"],
          "direction": "string"
        },
        "iconTags": {
          "icon": "string",
          "text": "string"
        }
      }
    }
  ],
  "error": "string",
  "status": "string",
  "message": "string"
}
```

## Supported Card Types

### V1 Card Types
- `HOTEL_CROSS_SELL`: Standard cross-sell recommendations
- `HOTEL_PERSONALISED`: Personalized hotel recommendations

### V2 Card Types
- `HOTEL_XSELL_BENEFITS`: Benefit-based cross-sell cards
- `HOTEL_EXTEND_YOUR_TRIP`: Extended trip recommendations
- `INTL_CASHBACK_CARD`: International cashback offers
- `GREAT_VALUE_PACKAGES_V2`: Value package recommendations

## Data Flow

```
1. Client Request
   ↓
2. Handler Processing
   ├── Validate Request
   ├── Set Streaming Headers
   └── Check Client Connection
   ↓
3. For each SearchEvent:
   ├── Create Channel
   ├── Launch Goroutine
   └── Process in Parallel
   ↓
4. Service Layer
   ├── ClientGateway (Hotel Search)
   ├── Static Data Retrieval
   ├── Rule Engine Processing (V2)
   └── Price Data Processing
   ↓
5. Response Processing
   ├── JSON Marshal
   └── Stream to Client
   ↓
6. Flush Response
   ↓
7. Next SearchEvent (if any)
```

## Error Handling

### Common Error Scenarios
- **Client Disconnect:** Detected via `http.CloseNotifier`
- **Invalid Request:** Returns 400 Bad Request
- **Downstream Service Failure:** Returns partial data with error indicators
- **Rule Engine Failure:** Falls back to standard processing (V2)
- **Timeout:** Configurable timeout for downstream calls

### Error Response Format
```json
{
  "error": "string",
  "status": "ERROR",
  "message": "Error description"
}
```

## Performance Considerations

### Optimization Features
- **Parallel Processing:** Multiple search events processed concurrently
- **Caching:** Couchbase integration for frequently accessed data
- **Streaming:** Real-time data delivery without waiting for all results
- **Connection Management:** Efficient handling of client disconnections

### Timeouts
- **Downstream Service Timeout:** 30 seconds (configurable)
- **Client Connection Timeout:** Based on client configuration
- **Streaming Flush Interval:** Immediate after each response

## Authentication & Security

### Authentication
- **Header-based:** `profileType` header for user type identification
- **MMT Auth:** `mmt-auth` header for user authentication
- **Correlation tracking:** UUID-based request correlation
- **Request validation:** Comprehensive input validation

### Data Protection
- **HTTPS only:** All communications encrypted
- **PII handling:** Sensitive data masked in logs
- **Input sanitization:** All inputs validated and sanitized

## Rate Limiting

### Limits
- **Requests per minute:** 500 (configurable)
- **Concurrent requests:** 50 (configurable)
- **Downstream service calls:** Based on service-specific limits

### Throttling
- **429 Too Many Requests:** When rate limit exceeded
- **Retry-After header:** Indicates when to retry
- **Graceful degradation:** Returns cached data when possible

## Usage Examples

### cURL Example (V1)
```bash
curl -X POST \
  http://localhost:8080/hotels-scion/api/cross-sell-stream \
  -H 'Content-Type: application/json' \
  -H 'Transfer-Encoding: chunked' \
  -d '{
    "brand": "MMT",
    "context": {
      "pageContext": "HOTEL_LANDING",
      "experimentData": "exp_123"
    },
    "searchEvent": [
      {
        "cardId": "HOTEL_CROSS_SELL",
        "sc": {
          "from": {"locus": {"id": "CTBOM"}},
          "to": {"locus": {"id": "CTDEL"}},
          "fromDateTime": {"str": "2024-01-15"},
          "toDateTime": {"str": "2024-01-17"},
          "pax": [{"count": 2, "details": {"adult": {"count": 2, "ages": [25, 30]}}}]
        }
      }
    ],
    "correlationKey": "req-123"
  }'
```

### cURL Example (V2)
```bash
curl -X POST \
  http://localhost:8080/hotels-scion/api/v2/cross-sell-stream \
  -H 'Content-Type: application/json' \
  -H 'Transfer-Encoding: chunked' \
  -d '{
    "brand": "MMT",
    "context": {
      "pageContext": "HOTEL_LANDING",
      "experimentData": "exp_123"
    },
    "searchEvent": [
      {
        "cardId": "HOTEL_XSELL_BENEFITS",
        "sc": {
          "from": {"locus": {"id": "CTBOM"}},
          "to": {"locus": {"id": "CTDEL"}},
          "fromDateTime": {"str": "2024-01-15"},
          "toDateTime": {"str": "2024-01-17"},
          "pax": [{"count": 2, "details": {"adult": {"count": 2, "ages": [25, 30]}}}]
        }
      }
    ],
    "correlationKey": "req-123"
  }'
```

### JavaScript Example
```javascript
const response = await fetch('/hotels-scion/api/v2/cross-sell-stream', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    brand: 'MMT',
    context: {
      pageContext: 'HOTEL_LANDING'
    },
    searchEvent: [{
      cardId: 'HOTEL_XSELL_BENEFITS',
      sc: {
        from: { locus: { id: 'CTBOM' } },
        to: { locus: { id: 'CTDEL' } },
        fromDateTime: { str: '2024-01-15' },
        toDateTime: { str: '2024-01-17' },
        pax: [{ count: 2, details: { adult: { count: 2, ages: [25, 30] } } }]
      }
    }],
    correlationKey: 'req-123'
  })
});

// Handle streaming response
const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const data = JSON.parse(chunk);
  console.log('Received data:', data);
}
```

## Implementation Details

### Handler Functions
- **V1:** `HandleCrossSellStreamRequest` in `handlers/crossell_handler.go`
- **V2:** `HandleCrossSellV2StreamRequest` in `handlers/crossell_handler.go`

### Service Functions
- **V1:** `GetCrossSellStreamResponse` in `services/crossell_service.go`
- **V2:** `GetCrossSellV2StreamResponse` and `GetCrossSellV2StreamResponseWithRuleEngineSupport` in `services/crossell_service.go`

### Request Models
- **Main Request:** `CrossSellRequest` in `models/request/cross_sell_stream_request.go`
- **Search Event:** `SearchEvent` in the same file

### Response Models
- **V1:** `CrossSellStreamResponse` in client gateway models
- **V2:** `CrossSellV2StreamResponse` in client gateway models

## Monitoring & Logging

### Logging
- Request/response logging with correlation keys
- Error logging for downstream service failures
- Performance metrics logging
- Client disconnect detection logging

### Metrics
- Request count and rate
- Response time distribution
- Error rate by type
- Downstream service latency
- Cache hit/miss ratios

## Configuration

### Environment Variables
- **Port:** Application port (default from config)
- **Log Level:** Logging verbosity
- **Cache Configuration:** Couchbase connection settings
- **Downstream URLs:** Service endpoint configurations

### Feature Flags
- Rule engine support (V2)
- Benefit cards feature
- Extended trip recommendations
- Caching strategies

---

*This documentation covers both V1 and V2 implementations of the cross-sell-stream API. For specific implementation details, refer to the source code in the handlers and services directories.*
