
-# Landing Card Discovery API

## Overview
The Landing Card Discovery API provides real-time streaming of landing discovery cards for hotel recommendations. It includes special logic for handpicked properties with city zone validation and mobile-optimized landing experiences.

**Endpoint:** `POST /hotels-scion/api/v2/landing-card-discovery`

## Architecture

### Type
- **Streaming API** with chunked transfer encoding
- **Pattern:** Landing discovery with city zone validation
- **Special Logic:** Handpicked properties card filtering based on city zones
- **Response:** Real-time streaming of landing discovery cards

### Key Features
- City zone-based card filtering
- Real-time data streaming
- Parallel processing of multiple search events
- Client disconnect detection
- Mobile landing page optimization

## Downstream Services

| Service | URL | Purpose |
|---------|-----|---------|
| **ClientGateway Landing Card Hotels** | `http://hotels-clientgateway-oth.ecs.mmt/clientbackend/entity/api/landingDiscovery` | Landing page hotel discovery |
| **ClientGateway Search Hotels** | `http://hotels-clientgateway-oth.ecs.mmt/clientbackend/entity/api/searchHotels` | Hotel search functionality |
| **HES Cards Data** | `http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/getCard` | Hotel card information |
| **Web API City Details** | `http://htlwebapi.ecs.mmt/hotels-entity/api/v4.0/hotels/suggest/getcitydetails` | City information |

## Request Format

### Headers
```
Content-Type: application/json
Transfer-Encoding: chunked
profileType: BUSINESS (optional)
```

### Request Body
```json
{
  "brand": "string",
  "context": {
    "contextId": "string",
    "experimentData": "string",
    "pageContext": "string",
    "scope": "string",
    "funnelSource": "string"
  },
  "secureUrl": "string",
  "imageCount": 0,
  "searchEvent": [
    {
      "cardId": "HANDPICKED_PROPERTIES",
      "templateId": "string",
      "componentId": "string",
      "limit": 0,
      "isFilter": false,
      "priority": 0,
      "hydraSegIds": ["string"],
      "meta": {},
      "sc": {
        "lob": "string",
        "lobCategory": "string",
        "fromDateTime": {
          "str": "2024-01-15",
          "ts": 1705276800,
          "zone": "Asia/Kolkata"
        },
        "toDateTime": {
          "str": "2024-01-17",
          "ts": 1705449600,
          "zone": "Asia/Kolkata"
        },
        "pax": [
          {
            "count": 2,
            "details": {
              "adult": {
                "ages": [25, 30],
                "count": 2
              },
              "child": {
                "ages": [],
                "count": 0
              },
              "infant": {
                "ages": [],
                "count": 0
              }
            }
          }
        ],
        "from": {
          "lobCity": "string",
          "locus": {
            "city": "string",
            "areaId": "string",
            "areaName": "string",
            "poiId": "string",
            "poiName": "string",
            "type": "city",
            "id": "CTBOM",
            "cityName": "Mumbai"
          },
          "cityName": "Mumbai",
          "countryName": "India",
          "countryCode": "IN",
          "longitude": 72.8777,
          "latitude": 19.076
        },
        "to": {
          "lobCity": "string",
          "locus": {
            "city": "string",
            "areaId": "string",
            "areaName": "string",
            "poiId": "string",
            "poiName": "string",
            "type": "city",
            "id": "CTDEL",
            "cityName": "Delhi"
          },
          "cityName": "Delhi",
          "countryName": "India",
          "countryCode": "IN",
          "longitude": 77.209,
          "latitude": 28.6139
        },
        "timestamp": 1705276800,
        "rooms": 1,
        "funnelSource": "string",
        "personalizedSearch": false,
        "product": {
          "id": "string",
          "name": "string"
        }
      },
      "selectedTabId": "string",
      "appliedFilterMap": {},
      "funnelActivity": {},
      "enrichments": {
        "trends": {
          "areas": [],
          "price_bucket": [],
          "ap_bucket": [],
          "los_bucket": [],
          "pax": [],
          "pd_id": [],
          "star_rating": [],
          "accommodation_type": [],
          "cities": []
        },
        "userPreferences": {}
      },
      "tags": {
        "edge_type": "string",
        "source": "string"
      },
      "filterList": [],
      "filter": false,
      "persuasionRequired": false,
      "recentlyViewedHotels": [],
      "isLandingDiscoveryPage": true,
      "recommendedPlaces": []
    }
  ],
  "nearBy": false,
  "offerRequired": false,
  "user": {
    "location": {
      "locationData": {
        "currentLocation": {
          "latitude": 19.076,
          "longitude": 72.8777
        }
      }
    }
  },
  "imageCategories": [],
  "correlationKey": "uuid-string",
  "metaInfo": {
    "userName": "string",
    "bookingId": "string"
  }
}
```

## Response Format

### Headers
```
Content-Type: application/json
Transfer-Encoding: chunked
```

### Response Body
```json
{
  "cardsV2": [
    {
      "cardDataV2": {
        "tabsData": {
          "HOTELS": {
            "hotels": [
              {
                "id": "hotel-123",
                "name": "Taj Mahal Palace",
                "images": [
                  "https://imgak.mmtcdn.com/hotels/123/image1.jpg",
                  "https://imgak.mmtcdn.com/hotels/123/image2.jpg"
                ],
                "starRating": 5,
                "userRating": 4.5,
                "currencyCode": "INR",
                "address": {
                  "line1": "Apollo Bunder",
                  "line2": "Mumbai, Maharashtra"
                },
                "displayFare": {
                  "displayPrice": 15000.0,
                  "nonDiscountedPrice": 18000.0
                },
                "appDeeplink": "mmyt://htl/listing/?hotelId=hotel-123&checkin=2024-01-15&checkout=2024-01-17",
                "desktopDeeplink": "https://www.makemytrip.com/hotels/hotel-details?hotelId=hotel-123&checkin=2024-01-15&checkout=2024-01-17"
              }
            ],
            "filters": [
              {
                "filterGroup": "PRICE_RANGE",
                "filterValue": "10000-20000",
                "title": "₹10000 - ₹20000",
                "rangeFilter": true
              }
            ],
            "persuasions": [
              {
                "text": "Get 15% off on luxury stays",
                "imageUrl": "https://imgak.mmtcdn.com/offers/luxury-offer.png"
              }
            ],
            "placeholders": [
              {
                "type": "HOTEL",
                "title": "More hotels coming soon",
                "description": "We're adding more handpicked properties"
              }
            ]
          },
          "HOMESTAYS": {
            "hotels": [
              {
                "id": "homestay-456",
                "name": "Cozy Homestay",
                "images": [
                  "https://imgak.mmtcdn.com/homestays/456/image1.jpg"
                ],
                "starRating": 4,
                "userRating": 4.2,
                "currencyCode": "INR",
                "address": {
                  "line1": "Bandra West",
                  "line2": "Mumbai, Maharashtra"
                },
                "displayFare": {
                  "displayPrice": 3000.0,
                  "nonDiscountedPrice": 3500.0
                },
                "appDeeplink": "mmyt://htl/listing/?hotelId=homestay-456&checkin=2024-01-15&checkout=2024-01-17",
                "desktopDeeplink": "https://www.makemytrip.com/hotels/hotel-details?hotelId=homestay-456&checkin=2024-01-15&checkout=2024-01-17"
              }
            ],
            "filters": [],
            "persuasions": [],
            "placeholders": []
          }
        },
        "header": {
          "heading": "Handpicked Properties in Mumbai",
          "subheading": "Curated selection of the best hotels and homestays"
        },
        "ctaList": [
          {
            "title": "View All Hotels",
            "deeplink": "https://www.makemytrip.com/hotels/hotel-listing?city=CTBOM"
          },
          {
            "title": "View All Homestays",
            "deeplink": "https://www.makemytrip.com/hotels/hotel-listing?city=CTBOM&homestay=true"
          }
        ]
      }
    }
  ]
}
```

## Data Flow

```
1. Client Request
   ↓
2. City Zone Validation
   ├── Check City Zone Map Configuration
   ├── Filter Valid Cards
   └── Remove Invalid Handpicked Properties
   ↓
3. For each SearchEvent:
   ├── Create Channel
   ├── Launch Goroutine
   └── Process in Parallel
   ↓
4. Landing Discovery Service
   ├── ClientGateway Landing Card Hotels
   ├── ClientGateway Search Hotels
   └── HES Cards Data
   ↓
5. Response Processing
   ├── Aggregate Cards
   ├── Process Tabs Data
   └── Build Response
   ↓
6. Stream Response to Client
   ↓
7. Flush to Client
```

## City Zone Configuration

### Supported Cities and Zones
```json
{
  "CTBOM": ["ZNMUMBA"],
  "CTDEL": ["ZNDELHI"],
  "CTBLR": ["ZNBENGA"],
  "CTJAI": ["ZNJAIPU"],
  "CTMAA": ["ZNCHEN"],
  "CTHYDERA": ["ZNHYD"],
  "CTCCU": ["ZNKOLK"],
  "CTPNQ": ["ZNPUN3744B525"],
  "RGNCR": ["ZNDELHI"],
  "CTGGN": ["ZNDELHI"],
  "CTXLK": ["ZNMUMBA"],
  "CTAMD": ["ZNAHMED"],
  "CTCOK": ["ZNCOCH"],
  "CTLKO": ["ZNLUC"],
  "CTAGR": ["ZNAGRA"],
  "ATATQ": ["ZNAMR"],
  "CTIXC": ["ZNCHAN"],
  "CTVNS": ["ZNVAR"],
  "CTIDR": ["ZNINDO"],
  "CTSXR": ["ZNSRI"],
  "CTCJB": ["ZNCOI"],
  "RGBOM": ["ZNMUMBA"],
  "RGCDRH": ["ZNCHAN"],
  "RGGGN": ["ZNDELHI"],
  "RGJDH": ["ZNJOD"]
}
```

### Card Filtering Logic
- **HANDPICKED_PROPERTIES:** Only shown for cities in the zone map
- **Other Cards:** Processed normally without zone restrictions
- **Invalid Cities:** Cards removed with warning logs

## Error Handling

### Common Error Scenarios
- **Client Disconnect:** Detected via `http.CloseNotifier`
- **Invalid City Zone:** Handpicked properties card removed
- **Downstream Service Failure:** Returns partial data with error indicators
- **Timeout:** Configurable timeout for downstream calls

### Error Response Format
```json
{
  "cardsV2": [],
  "error": "string",
  "status": "ERROR",
  "message": "Error description"
}
```

### Warning Scenarios
```json
{
  "warning": "Removed HANDPICKED PROPERTIES cards from the request, CityId: CTINVALID not present in the map configuration"
}
```

## Performance Considerations

### Optimization Features
- **City Zone Caching:** Zone map loaded at startup
- **Parallel Processing:** Multiple search events processed concurrently
- **Streaming:** Real-time data delivery without waiting for all results
- **Connection Management:** Efficient handling of client disconnections

### Timeouts
- **Downstream Service Timeout:** 30 seconds (configurable)
- **Client Connection Timeout:** Based on client configuration
- **Streaming Flush Interval:** Immediate after each response

## Usage Examples

### cURL Example
```bash
curl -X POST \
  http://localhost:8080/hotels-scion/api/v2/landing-card-discovery \
  -H 'Content-Type: application/json' \
  -H 'Transfer-Encoding: chunked' \
  -d '{
    "brand": "MMT",
    "context": {
      "pageContext": "HOTEL_LANDING",
      "experimentData": "exp_123"
    },
    "searchEvent": [
      {
        "cardId": "HANDPICKED_PROPERTIES",
        "sc": {
          "from": {"locus": {"id": "CTBOM"}},
          "to": {"locus": {"id": "CTDEL"}},
          "fromDateTime": {"str": "2024-01-15"},
          "toDateTime": {"str": "2024-01-17"},
          "pax": [{"count": 2, "details": {"adult": {"count": 2, "ages": [25, 30]}}}]
        }
      }
    ],
    "correlationKey": "req-123"
  }'
```

### JavaScript Example
```javascript
const response = await fetch('/hotels-scion/api/v2/landing-card-discovery', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    brand: 'MMT',
    context: {
      pageContext: 'HOTEL_LANDING'
    },
    searchEvent: [{
      cardId: 'HANDPICKED_PROPERTIES',
      sc: {
        from: { locus: { id: 'CTBOM' } },
        to: { locus: { id: 'CTDEL' } },
        fromDateTime: { str: '2024-01-15' },
        toDateTime: { str: '2024-01-17' },
        pax: [{ count: 2, details: { adult: { count: 2, ages: [25, 30] } } }]
      }
    }],
    correlationKey: 'req-123'
  })
});

// Handle streaming response
const reader = response.body.getReader();
while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = new TextDecoder().decode(value);
  const data = JSON.parse(chunk);
  console.log('Landing Discovery:', data);
}
```

## Monitoring and Logging

### Key Metrics
- **Response Time:** Per search event processing time
- **Throughput:** Number of requests per second
- **Error Rate:** Percentage of failed requests
- **City Zone Filtering:** Number of cards filtered by zone

### Logging
- **Request Logging:** Full request payload with correlation key
- **Response Logging:** Response status and timing
- **City Zone Logging:** Zone validation results and warnings
- **Performance Logging:** Downstream service call timings

## Rate Limiting

### Limits
- **Requests per minute:** 1000 (configurable)
- **Concurrent connections:** 100 (configurable)
- **Downstream service calls:** Based on service-specific limits

### Throttling
- **429 Too Many Requests:** When rate limit exceeded
- **Retry-After header:** Indicates when to retry
- **Graceful degradation:** Returns cached data when possible

## Security

### Authentication
- **Header-based:** `profileType` header for user type
- **Correlation tracking:** UUID-based request correlation
- **Request validation:** Comprehensive input validation

### Data Protection
- **HTTPS only:** All communications encrypted
- **PII handling:** Sensitive data masked in logs
- **Input sanitization:** All inputs validated and sanitized

## Supported Card Types

### Landing Discovery Cards
- **HANDPICKED_PROPERTIES:** Curated hotel and homestay selection
- **GREAT_VALUE_PACKAGES_V2:** Value package deals
- **LOCATION_BASED_CARDS:** Location-specific recommendations
- **TRENDING_CARDS:** Popular and trending properties

### Card Features
- **Tabs Data:** Multiple tabs (Hotels, Homestays, etc.)
- **Filters:** Applied filter information
- **Persuasions:** Promotional content
- **Placeholders:** Loading and empty state content
- **CTA Lists:** Call-to-action buttons 